<template>
  <div class="project-info">
    <!-- Mobile view with collapsible info panel -->
    <div class="md:hidden">
      <button
        ref="toggleButton"
        @click="toggleMobileInfo"
        class="w-full flex items-center justify-between p-4 rounded-lg transition-all duration-300 relative overflow-hidden group"
        :style="{
          backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
          borderLeft: `3px solid ${project.meta.colors?.primary || '#00FF41'}`
        }"
      >
        <!-- Animated background glow -->
        <div
          ref="buttonGlow"
          class="absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none"
          :style="{ backgroundColor: `${project.meta.colors?.primary || '#00FF41'}10` }"
        ></div>

        <div class="flex items-center relative z-10">
          <IconifyIcon
            ref="infoIcon"
            icon="mdi:information-outline"
            class="mr-3 w-6 h-6 text-white transition-all duration-300"
          />
          <span class="font-medium text-white text-lg">Project Details</span>
        </div>

        <!-- Enhanced chevron with rotation animation -->
        <div class="relative z-10 flex items-center">
          <IconifyIcon
            ref="chevronIcon"
            :icon="showMobileInfo ? 'mdi:chevron-up' : 'mdi:chevron-down'"
            class="w-6 h-6 text-white transition-all duration-500"
          />

          <!-- Ripple effect container -->
          <div
            ref="buttonRipple"
            class="absolute inset-0 rounded-full opacity-0 pointer-events-none"
            :style="{ backgroundColor: `${project.meta.colors?.primary || '#00FF41'}30` }"
          ></div>
        </div>
      </button>

      <!-- Enhanced collapsible content -->
      <div
        v-show="showMobileInfo"
        class="mt-4 overflow-hidden"
        ref="mobileInfoPanel"
      >
        <div ref="contentContainer" class="opacity-0">
          <GlassContainer class="mb-4 relative overflow-hidden">
            <!-- Animated border glow -->
            <div
              ref="containerGlow"
              class="absolute inset-0 opacity-0 pointer-events-none rounded-lg"
              :style="{
                boxShadow: `0 0 20px ${project.meta.colors?.primary || '#00FF41'}20`,
                border: `1px solid ${project.meta.colors?.primary || '#00FF41'}30`
              }"
            ></div>
          <!-- Project Duration -->
          <div v-if="projectDuration" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Duration</h3>
            <div class="flex items-center gap-2">
              <div
                class="duration-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                }"
              >
                <IconifyIcon icon="mdi:clock-outline" class="w-3.5 h-3.5 text-white" />
                <span>{{ projectDuration }}</span>
              </div>
            </div>
          </div>

          <!-- Platform -->
          <div v-if="projectPlatform" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Platform</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(platform, index) in platformList"
                :key="index"
                class="platform-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                }"
              >
                <IconifyIcon :icon="getPlatformIcon(platform)" class="w-3.5 h-3.5 text-white" />
                <span>{{ platform }}</span>
              </div>
            </div>
          </div>

          <!-- Tech stack -->
          <div v-if="project.meta.techStack" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Tech Stack</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(tech, index) in project.meta.techStack"
                :key="index"
                class="tech-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                }"
              >
                <IconifyIcon v-if="tech.icon" :icon="tech.icon" class="w-3.5 h-3.5 text-white" />
                <span>{{ tech.name }}</span>
              </div>
            </div>
          </div>

          <!-- Tags -->
          <div v-if="project.meta.tags" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Project Type</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(tag, index) in project.meta.tags"
                :key="index"
                class="tag-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                }"
              >
                <IconifyIcon v-if="tag.icon" :icon="tag.icon" class="w-3.5 h-3.5 text-white" />
                <span>{{ tag.type }}</span>
              </div>
            </div>
          </div>

          <!-- Roles -->
          <div v-if="project.meta.roles" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">My Roles</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(role, index) in project.meta.roles"
                :key="index"
                class="role-item px-2 py-1 rounded-full text-xs text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                }"
              >
                {{ role }}
              </div>
            </div>
          </div>

          <!-- Project buttons -->
          <div v-if="project.meta.info?.buttons">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Links</h3>
            <div class="flex flex-wrap gap-2">
              <a
                v-for="(button, index) in project.meta.info.buttons"
                :key="index"
                :href="button.url"
                target="_blank"
                rel="noopener noreferrer"
                class="project-button inline-flex items-center px-3 py-1.5 rounded-lg transition-all duration-300 text-sm text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.2)`,
                  border: `1px solid ${project.meta.colors?.primary || '#00FF41'}`
                }"
              >
                <span>{{ button.text }}</span>
                <IconifyIcon icon="mdi:open-in-new" class="ml-1.5 w-3.5 h-3.5 text-white" />
              </a>
            </div>
          </div>
          </GlassContainer>
        </div>
      </div>
    </div>

    <!-- Desktop view with sidebar layout -->
    <div class="hidden md:block">
      <GlassContainer class="sidebar-info">
        <!-- Project Duration -->
        <div v-if="projectDuration" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Duration</h3>
          <div class="flex items-center gap-2">
            <div
              class="duration-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
              }"
            >
              <IconifyIcon icon="mdi:clock-outline" class="w-3.5 h-3.5 text-white" />
              <span>{{ projectDuration }}</span>
            </div>
          </div>
        </div>

        <!-- Platform -->
        <div v-if="projectPlatform" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Platform</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(platform, index) in platformList"
              :key="index"
              class="platform-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
              }"
            >
              <IconifyIcon :icon="getPlatformIcon(platform)" class="w-3.5 h-3.5 text-white" />
              <span>{{ platform }}</span>
            </div>
          </div>
        </div>

        <!-- Tech stack -->
        <div v-if="project.meta.techStack" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Tech Stack</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(tech, index) in project.meta.techStack"
              :key="index"
              class="tech-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
              }"
            >
              <IconifyIcon v-if="tech.icon" :icon="tech.icon" class="w-3.5 h-3.5 text-white" />
              <span>{{ tech.name }}</span>
            </div>
          </div>
        </div>

        <!-- Tags -->
        <div v-if="project.meta.tags" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Project Type</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(tag, index) in project.meta.tags"
              :key="index"
              class="tag-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
              }"
            >
              <IconifyIcon v-if="tag.icon" :icon="tag.icon" class="w-3.5 h-3.5 text-white" />
              <span>{{ tag.type }}</span>
            </div>
          </div>
        </div>

        <!-- Roles -->
        <div v-if="project.meta.roles" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">My Roles</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(role, index) in project.meta.roles"
              :key="index"
              class="role-item px-2 py-1 rounded-full text-xs text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
              }"
            >
              {{ role }}
            </div>
          </div>
        </div>

        <!-- Project buttons -->
        <div v-if="project.meta.info?.buttons">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Links</h3>
          <div class="flex flex-col gap-2">
            <a
              v-for="(button, index) in project.meta.info.buttons"
              :key="index"
              :href="button.url"
              target="_blank"
              rel="noopener noreferrer"
              class="project-button inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 text-sm text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.2)`,
                border: `1px solid ${project.meta.colors?.primary || '#00FF41'}`
              }"
            >
              <span>{{ button.text }}</span>
              <IconifyIcon icon="mdi:open-in-new" class="ml-1.5 w-3.5 h-3.5 text-white" />
            </a>
          </div>
        </div>
      </GlassContainer>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { gsap } from 'gsap';
import GlassContainer from '~/components/ui/GlassContainer.vue';

const props = defineProps({
  project: {
    type: Object,
    required: true
  }
});

// Convert hex color to RGB values
const hexToRgb = (hex) => {
  // Default color if no hex is provided
  if (!hex) return { r: 66, g: 184, b: 131 }; // Default green color

  // Remove # if present
  hex = hex.replace(/^#/, '');

  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;

  return { r, g, b };
};

// State for mobile info panel
const showMobileInfo = ref(false);
const mobileInfoPanel = ref(null);

// New refs for enhanced animations
const toggleButton = ref(null);
const buttonGlow = ref(null);
const infoIcon = ref(null);
const chevronIcon = ref(null);
const buttonRipple = ref(null);
const contentContainer = ref(null);
const containerGlow = ref(null);

// Enhanced button ripple effect
const createButtonRipple = () => {
  if (buttonRipple.value) {
    gsap.fromTo(buttonRipple.value, {
      scale: 0,
      opacity: 0.8
    }, {
      scale: 2,
      opacity: 0,
      duration: 0.6,
      ease: 'power2.out'
    });
  }
};

// Enhanced button hover effects
const animateButtonHover = (isHovering) => {
  if (!buttonGlow.value || !infoIcon.value) return;

  if (isHovering) {
    gsap.to(buttonGlow.value, {
      opacity: 1,
      duration: 0.3,
      ease: 'power2.out'
    });

    gsap.to(infoIcon.value, {
      scale: 1.1,
      rotation: 5,
      duration: 0.3,
      ease: 'back.out(1.7)'
    });
  } else {
    gsap.to(buttonGlow.value, {
      opacity: 0,
      duration: 0.3,
      ease: 'power2.in'
    });

    gsap.to(infoIcon.value, {
      scale: 1,
      rotation: 0,
      duration: 0.3,
      ease: 'power2.out'
    });
  }
};

// Enhanced chevron animation
const animateChevron = (isExpanded) => {
  if (!chevronIcon.value) return;

  gsap.to(chevronIcon.value, {
    rotation: isExpanded ? 180 : 0,
    scale: isExpanded ? 1.1 : 1,
    duration: 0.5,
    ease: 'back.out(1.7)'
  });
};

// Enhanced content animation
const animateContent = (isExpanding) => {
  if (!contentContainer.value || !containerGlow.value) return;

  if (isExpanding) {
    // Expanding animation
    const tl = gsap.timeline();

    // First expand the container
    tl.fromTo(mobileInfoPanel.value, {
      height: 0
    }, {
      height: 'auto',
      duration: 0.5,
      ease: 'power3.out'
    })
    // Then fade in the content with a spring effect
    .fromTo(contentContainer.value, {
      opacity: 0,
      y: -20,
      scale: 0.95
    }, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.4,
      ease: 'back.out(1.4)'
    }, 0.2)
    // Add container glow effect
    .to(containerGlow.value, {
      opacity: 1,
      duration: 0.3,
      ease: 'power2.out'
    }, 0.3)
    // Animate individual content items
    .fromTo('.tech-item, .tag-item, .role-item, .duration-item, .platform-item, .project-button', {
      opacity: 0,
      x: -20,
      rotationY: -15
    }, {
      opacity: 1,
      x: 0,
      rotationY: 0,
      stagger: {
        each: 0.05,
        from: 'start'
      },
      duration: 0.3,
      ease: 'back.out(1.7)'
    }, 0.4);
  } else {
    // Collapsing animation
    const tl = gsap.timeline();

    // First animate content items out
    tl.to('.tech-item, .tag-item, .role-item, .duration-item, .platform-item, .project-button', {
      opacity: 0,
      x: 20,
      rotationY: 15,
      stagger: {
        each: 0.03,
        from: 'end'
      },
      duration: 0.2,
      ease: 'power2.in'
    })
    // Remove container glow
    .to(containerGlow.value, {
      opacity: 0,
      duration: 0.2,
      ease: 'power2.in'
    }, 0.1)
    // Fade out content container
    .to(contentContainer.value, {
      opacity: 0,
      y: -10,
      scale: 0.98,
      duration: 0.3,
      ease: 'power2.in'
    }, 0.2)
    // Finally collapse the panel
    .to(mobileInfoPanel.value, {
      height: 0,
      duration: 0.4,
      ease: 'power3.inOut'
    }, 0.3);
  }
};

// Enhanced toggle mobile info panel
const toggleMobileInfo = () => {
  // Create button ripple effect
  createButtonRipple();

  // Toggle state
  showMobileInfo.value = !showMobileInfo.value;

  // Animate chevron
  animateChevron(showMobileInfo.value);

  // Animate content
  if (mobileInfoPanel.value && contentContainer.value) {
    animateContent(showMobileInfo.value);
  }
};

// Extract project duration from info table
const projectDuration = computed(() => {
  if (!props.project?.meta.info?.table) return null;

  // Find the duration entry in the table
  const durationEntry = props.project.meta.info.table.find(item =>
    item.label.toLowerCase() === 'duration'
  );

  return durationEntry ? durationEntry.value : null;
});

// Extract project platform from info table
const projectPlatform = computed(() => {
  if (!props.project?.meta.info?.table) return null;

  // Find the platform entry in the table
  const platformEntry = props.project.meta.info.table.find(item =>
    item.label.toLowerCase() === 'platform'
  );

  return platformEntry ? platformEntry.value : null;
});

// Convert platform string to array of platforms
const platformList = computed(() => {
  if (!projectPlatform.value) return [];

  // Split by commas or slashes and trim whitespace
  return projectPlatform.value
    .split(/[,\/]/)
    .map(platform => platform.trim())
    .filter(platform => platform.length > 0);
});

// Get appropriate icon for platform
const getPlatformIcon = (platform) => {
  const platformLower = platform.toLowerCase();

  if (platformLower.includes('web')) return 'mdi:web';
  if (platformLower.includes('windows')) return 'mdi:microsoft-windows';
  if (platformLower.includes('mac')) return 'mdi:apple';
  if (platformLower.includes('ios')) return 'mdi:apple-ios';
  if (platformLower.includes('android')) return 'mdi:android';
  if (platformLower.includes('linux')) return 'mdi:linux';
  if (platformLower.includes('playstation') || platformLower.includes('ps')) return 'mdi:sony-playstation';
  if (platformLower.includes('xbox')) return 'mdi:microsoft-xbox';
  if (platformLower.includes('nintendo') || platformLower.includes('switch')) return 'mdi:nintendo-switch';
  if (platformLower.includes('mobile')) return 'mdi:cellphone';
  if (platformLower.includes('vr') || platformLower.includes('virtual reality')) return 'mdi:virtual-reality';
  if (platformLower.includes('meta quest') || platformLower.includes('oculus')) return 'ri:meta-fill';

  // Default icon for other platforms
  return 'mdi:devices';
};

// Compute RGB values from project primary color
const rgbValues = computed(() => {
  if (!props.project || !props.project.meta.colors || !props.project.meta.colors.primary) {
    return { r: 66, g: 184, b: 131 }; // Default green color
  }

  const primaryColor = props.project.meta.colors.primary;
  // Remove # if present and ensure it's a valid hex color
  const cleanHex = primaryColor.replace(/^#/, '');
  return hexToRgb(cleanHex);
});

// Initialize animations
onMounted(() => {
  // Set initial state for mobile panel
  if (mobileInfoPanel.value && !showMobileInfo.value) {
    gsap.set(mobileInfoPanel.value, { height: 0, opacity: 0 });
  }

  // Set initial state for content container
  if (contentContainer.value) {
    gsap.set(contentContainer.value, { opacity: 0 });
  }

  // Add hover listeners to toggle button
  if (toggleButton.value) {
    toggleButton.value.addEventListener('mouseenter', () => animateButtonHover(true));
    toggleButton.value.addEventListener('mouseleave', () => animateButtonHover(false));
  }
});
</script>

<style scoped>
.project-button {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.project-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.project-button:active {
  transform: translateY(0);
}

.tech-item, .tag-item, .role-item, .duration-item, .platform-item {
  transition: all 0.3s ease;
}

.tech-item:hover, .tag-item:hover, .role-item:hover, .duration-item:hover, .platform-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Sidebar styling */
.sidebar-info {
  position: sticky;
  top: 1.5rem;
}


</style>
