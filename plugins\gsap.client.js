// Optimized GSAP plugin with performance improvements and memory management
import { gsap } from 'gsap';
import { CSSPlugin } from 'gsap/CSSPlugin';

export default defineNuxtPlugin((nuxtApp) => {
  // Register CSSPlugin explicitly
  gsap.registerPlugin(CSSPlugin);

  // Optimized GSAP configuration for better performance
  gsap.defaults({
    ease: 'power2.out',
    duration: 0.8,
    overwrite: 'auto'
  });

  // Global timeline for coordinated animations
  let masterTimeline = null;

  // Animation registry for cleanup
  const activeAnimations = new Set();
  const animationObserver = new WeakMap();

  // Performance-optimized animation utilities
  const animations = {
    // Create timeline with automatic cleanup registration
    createTimeline(options = {}) {
      const timeline = gsap.timeline({
        ...options,
        onComplete: () => {
          activeAnimations.delete(timeline);
          options.onComplete?.();
        }
      });

      activeAnimations.add(timeline);
      return timeline;
    },

    // Batch animation creation for better performance
    createBatch(elements, animationType, options = {}) {
      if (!elements || elements.length === 0) return null;

      const batch = gsap.timeline();
      const defaults = this.getDefaults(animationType);
      const mergedOptions = { ...defaults, ...options };

      batch.from(elements, mergedOptions);
      activeAnimations.add(batch);

      return batch;
    },

    // Get default options for animation types
    getDefaults(type) {
      const defaults = {
        fadeInFromTop: { y: -30, opacity: 0, duration: 0.8, ease: 'power2.out' },
        fadeInFromBottom: { y: 20, opacity: 0, duration: 0.5, ease: 'power2.out' },
        fadeIn: { opacity: 0, duration: 0.5, ease: 'power2.out' },
        staggerFadeIn: { opacity: 0, y: 20, duration: 0.6, stagger: 0.1, ease: 'power2.out' },
        slideIn: { x: 50, opacity: 0, duration: 0.6, ease: 'power2.out' },
        scaleIn: { scale: 0.8, opacity: 0, duration: 0.5, ease: 'back.out(1.7)' }
      };

      return defaults[type] || defaults.fadeIn;
    },

    // Optimized fade in from top
    fadeInFromTop(element, options = {}) {
      if (!element) return null;
      return this.createBatch([element], 'fadeInFromTop', options);
    },

    // Optimized fade in from bottom
    fadeInFromBottom(element, options = {}) {
      if (!element) return null;
      return this.createBatch([element], 'fadeInFromBottom', options);
    },

    // Optimized fade in
    fadeIn(element, options = {}) {
      if (!element) return null;
      return this.createBatch([element], 'fadeIn', options);
    },

    // Optimized staggered animations
    staggerFadeIn(elements, options = {}) {
      if (!elements || elements.length === 0) return null;
      return this.createBatch(elements, 'staggerFadeIn', options);
    },

    // Scale in animation
    scaleIn(element, options = {}) {
      if (!element) return null;
      return this.createBatch([element], 'scaleIn', options);
    },

    // Slide in animation
    slideIn(element, options = {}) {
      if (!element) return null;
      return this.createBatch([element], 'slideIn', options);
    },

    // Optimized ripple effect with better performance
    createRipple(element, event, color = '#ffffff') {
      if (!element || !event) return null;

      try {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        // Create ripple element with optimized styles
        const ripple = document.createElement('span');
        ripple.className = 'ripple';

        // Use hardware acceleration optimizations
        gsap.set(ripple, {
          position: 'absolute',
          borderRadius: '50%',
          background: color,
          scale: 0,
          pointerEvents: 'none',
          width: size,
          height: size,
          left: x,
          top: y,
          opacity: 0.6,
          zIndex: 1000,
          willChange: 'transform, opacity', // Optimize for animations
          force3D: true // Enable hardware acceleration
        });

        // Ensure parent is positioned
        if (getComputedStyle(element).position === 'static') {
          element.style.position = 'relative';
        }
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        // Optimized animation with cleanup
        const animation = gsap.to(ripple, {
          scale: 4,
          opacity: 0,
          duration: 0.6,
          ease: 'power2.out',
          onComplete: () => {
            try {
              if (ripple?.parentNode) {
                ripple.parentNode.removeChild(ripple);
              }
              activeAnimations.delete(animation);
            } catch (error) {
              console.warn('Error removing ripple:', error);
            }
          }
        });

        activeAnimations.add(animation);
        return animation;
      } catch (error) {
        console.error('Error creating ripple effect:', error);
        return null;
      }
    },

    // Batch cleanup for better performance
    cleanup() {
      activeAnimations.forEach(animation => {
        try {
          animation.kill();
        } catch (error) {
          console.warn('Error killing animation:', error);
        }
      });
      activeAnimations.clear();

      // Clean up ripples
      try {
        document.querySelectorAll('.ripple').forEach(ripple => {
          ripple?.parentNode?.removeChild(ripple);
        });
      } catch (error) {
        console.warn('Error cleaning up ripples:', error);
      }
    },

    // Get active animation count for debugging
    getActiveCount() {
      return activeAnimations.size;
    }
  };

  // Optimized page transition cleanup
  nuxtApp.hook('page:transition:finish', () => {
    // Use batch cleanup for better performance
    animations.cleanup();
  });

  // Cleanup on app unmount
  nuxtApp.hook('app:beforeUnmount', () => {
    animations.cleanup();
  });

  // Performance monitoring (development only)
  if (process.dev) {
    nuxtApp.hook('page:finish', () => {
      console.log(`Active GSAP animations: ${animations.getActiveCount()}`);
    });
  }

  // Provide optimized GSAP utilities
  return {
    provide: {
      gsap,
      animations
    }
  };
});
