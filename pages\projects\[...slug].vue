<template>
  <main class="min-h-screen bg-transparent project-detail-page">
    <!-- Loading indicator -->
    <div v-if="isLoading" class="fixed inset-0 flex items-center justify-center z-50 bg-black/70">
      <div class="text-center">
        <div class="w-16 h-16 border-4 border-t-transparent rounded-full animate-spin mx-auto"
             :style="{ borderColor: `transparent ${themeStore.themeColors.primary || '#00FF41'} ${themeStore.themeColors.primary || '#00FF41'} ${themeStore.themeColors.primary || '#00FF41'}` }">
        </div>
        <p class="mt-4 text-white text-lg">Loading project...</p>
      </div>
    </div>

    <div v-if="doc" class="relative">
      <!-- Project Hero Section with Cover Image -->
      <div class="relative h-64 md:h-80 lg:h-96 overflow-hidden" style="margin-top: 0;">
        <!-- Simple gradient overlay for visual appeal -->
        <div class="absolute inset-0 bg-gradient-to-b from-black/10 to-black/30 z-10"></div>

        <img
          v-if="doc.meta.coverImage"
          :src="doc.meta.coverImage"
          :alt="doc.title"
          class="w-full h-full object-cover"
          loading="eager"
        />
        <div v-else class="w-full h-full bg-gradient-to-br"
             style="background-image: linear-gradient(to bottom right, #42b883, #347474);"
             :style="typeof window !== 'undefined' ? {
               backgroundImage: `linear-gradient(to bottom right, ${doc.meta.colors?.primary || '#42b883'}, ${doc.meta.colors?.secondary || '#347474'})`
             } : {}"
        ></div>

        <!-- Back Button -->
        <div class="absolute top-4 right-4 z-20 md:top-6 md:right-6">
          <button
            @click="navigateBackToProjects"
            class="flex items-center text-white px-3 py-2 rounded-full transition-all duration-300 back-button"
            :style="{
              backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.8)`,
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
              borderColor: doc?.meta.colors?.primary || '#42b883'
            }"
          >
            <IconifyIcon icon="mdi:arrow-left" class="mr-1.5 w-4 h-4" />
            <span class="text-sm font-medium">Back</span>
          </button>
        </div>
      </div>

      <!-- Project Content Container -->
      <div class="container mx-auto px-4 py-4 md:py-6 max-w-7xl">
        <!-- Project Info Section - Now above content -->
        <div class="mb-6 md:mb-8">
          <ProjectInfo :project="doc" />
        </div>

        <!-- Main content -->
        <div class="w-full">
          <!-- Project Content -->
          <div class="project-content content-backdrop">
            <div class="prose prose-sm md:prose-lg prose-invert w-full max-w-none">
              <ContentRenderer :value="doc">
                <template #empty>
                  <p class="text-white">No content found.</p>
                </template>
              </ContentRenderer>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup>
import { ref, onMounted, watch, computed, provide } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { gsap } from 'gsap';
import { useThemeStore } from '~/stores/theme-optimized';
import ProjectInfo from '~/components/projects/ProjectInfo.vue';

// Provide a flag to indicate that the first h1 should be skipped
// since we're already showing the title in the hero section
provide('isFirstH1', true);

// Loading state
const isLoading = ref(true);
const projectTitle = ref(null);
const route = useRoute();
const router = useRouter();
const themeStore = useThemeStore();

// Convert hex color to RGB values
const hexToRgb = (hex) => {
  // Default color if no hex is provided
  if (!hex) return { r: 66, g: 184, b: 131 }; // Default green color

  // Remove # if present
  hex = hex.replace(/^#/, '');

  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;

  return { r, g, b };
};

// Compute RGB values from project primary color with improved error handling
const rgbValues = computed(() => {
  // For SSR, return a consistent default to prevent hydration mismatches
  if (typeof window === 'undefined') {
    return { r: 66, g: 184, b: 131 }; // Default green color
  }

  try {
    if (!doc.value || !doc.value.meta.colors || !doc.value.meta.colors.primary) {
      return { r: 66, g: 184, b: 131 }; // Default green color
    }

    const primaryColor = doc.value.meta.colors.primary;
    // Remove # if present and ensure it's a valid hex color
    const cleanHex = primaryColor.replace(/^#/, '');
    return hexToRgb(cleanHex);
  } catch (error) {
    // Silent fallback to default
    return { r: 66, g: 184, b: 131 }; // Default green color
  }
});

// Function to navigate back to projects and reset theme
const navigateBackToProjects = () => {
  // Reset to default theme using the theme store
  // The resetToDefaultTheme method now preserves the game/web preference
  themeStore.resetToDefaultTheme();

  // Force scroll to top before navigation
  forceScrollToTop();

  // Navigate to projects page
  router.push('/projects');
};

// Extract the slug from the route
const slug = computed(() => {
  // Remove '/projects/' from the path to get the slug
  return route.path.replace('/projects/', '');
});

// Use the slug as part of the key to force refresh when route changes
const { data: doc, pending } = await useAsyncData(
  `project-${slug.value}`,
  () => queryCollection("content")
  .path(route.path).first(),
  {
    // Enable server-side prefetching for faster initial load
    server: true,
    // Cache the result to improve performance
    cache: true,
    // Add a longer cache time (24 hours in milliseconds)
    getCachedData: (key) => {
      // Check if we're in a browser environment
      if (typeof window !== 'undefined') {
        try {
          // Try to get cached data from localStorage
          const cached = localStorage.getItem(`project_cache_${key}`);
          if (cached) {
            const { data, timestamp } = JSON.parse(cached);
            // Check if cache is still valid (less than 24 hours old)
            if (Date.now() - timestamp < 24 * 60 * 60 * 1000) {
              return data;
            }
          }
        } catch (e) {
          // Silent fallback
        }
      }
      return null;
    },
    // Add a function to cache the data
    watch: false, // Disable automatic refreshing to improve performance
    lazy: false // Ensure data is loaded immediately
  }
);

// Watch for pending state changes to update loading state
watch(pending, (isPending) => {
  isLoading.value = isPending;
});

// Set project theme during SSR to prevent hydration mismatches
if (doc.value?.meta.colors) {
  // Create a project colors object with all necessary properties
  const projectColors = {
    ...doc.value.meta.colors,
    background: doc.value.meta.colors.background || generateDarkBackground(doc.value.meta.colors.primary)
  };

  // Apply the theme using the theme store's method
  // This will set all necessary properties for both SSR and client
  themeStore.setProjectTheme(projectColors);
}


// Watch for route changes to refresh the content and update theme
watch(() => route.path, async (newPath) => {
  // Skip during SSR to prevent hydration mismatches
  if (typeof window === 'undefined') return;

  try {
    // Only process if we're still on a project page
    if (!newPath.startsWith('/projects/') || newPath === '/projects/') {
      return;
    }

    // Set loading state
    isLoading.value = true;

    // Force scroll to top immediately
    forceScrollToTop();

    // And again after a short delay
    setTimeout(forceScrollToTop, 50);
    setTimeout(forceScrollToTop, 200);

    const newSlug = newPath.replace('/projects/', '');

    // Force refresh the content when route changes
    await refreshNuxtData(`project-${newSlug}`);

    // Fetch the content directly to ensure we have the latest data
    const freshContent = await queryCollection("content").path('/projects').first();

    if (freshContent?.meta.colors) {
      // Manually update the theme with the fresh content
      const projectColors = {
        ...freshContent.colors,
        background: freshContent.meta.colors.background || generateDarkBackground(freshContent.meta.colors.primary)
      };

      // Apply the theme using the theme store
      themeStore.setProjectTheme(projectColors);
    }

    // Set loading state to false when done
    isLoading.value = false;
  } catch (error) {
    // Silent fallback
    // Try to reset theme as a fallback
    try {
      themeStore.resetToDefaultTheme();
    } catch (resetError) {
      // Silent fallback
    }
    // Make sure loading state is set to false even on error
    isLoading.value = false;
  }
});

// Function to disable any scroll libraries that might interfere
const disableScrollLibraries = () => {
  if (typeof window === 'undefined' || !document) return;

  try {
    // Try to access the Locomotive Scroll instance if it exists
    const nuxtApp = useNuxtApp();
    if (nuxtApp && nuxtApp.$locomotiveScroll && typeof nuxtApp.$locomotiveScroll.destroy === 'function') {
      nuxtApp.$locomotiveScroll.destroy();
    }

    // Safely access DOM elements
    if (document.documentElement) {
      document.documentElement.style.scrollBehavior = 'auto';
      document.documentElement.classList.remove('has-scroll-init', 'has-scroll-smooth');
    }

    if (document.body) {
      document.body.style.scrollBehavior = 'auto';
      document.body.classList.remove('has-scroll-init', 'has-scroll-smooth');
    }
  } catch (error) {
    // Silently ignore errors during hot reload
  }
};

// Function to force scroll to top
const forceScrollToTop = () => {
  if (typeof window === 'undefined' || !document) return;

  try {
    // First disable any scroll libraries
    disableScrollLibraries();

    // Use multiple methods to ensure it works
    window.scrollTo(0, 0);
    window.scrollTo({ top: 0, left: 0, behavior: 'auto' });

    // Safely access DOM elements
    if (document.documentElement) document.documentElement.scrollTop = 0;
    if (document.body) document.body.scrollTop = 0;

    // Try to find the main content element and scroll it
    try {
      const main = document.querySelector('main');
      if (main) main.scrollTop = 0;
    } catch (mainError) {
      // Ignore main element errors
    }

    // Try to find any scrollable containers and reset them
    try {
      document.querySelectorAll('.scroll-container, [data-scroll-container]').forEach(container => {
        if (container) container.scrollTop = 0;
      });
    } catch (containerError) {
      // Ignore container errors
    }

    // Use history API to force a scroll reset
    try {
      if (history && history.scrollRestoration) {
        history.scrollRestoration = 'manual';
      }
    } catch (historyError) {
      // Ignore history API errors
    }
  } catch (error) {
    // Silently ignore errors during hot reload
  }
};

// Update theme when project data is available
onMounted(async () => {
  // Skip all client-side operations during SSR
  if (typeof window === 'undefined') return;

  try {
    // Set loading state to false once mounted
    // The initial data should already be loaded from useAsyncData
    isLoading.value = false;

    // Force scroll to top immediately
    forceScrollToTop();

    // And again after a short delay
    setTimeout(forceScrollToTop, 50);
    setTimeout(forceScrollToTop, 200);

    // Ensure we're on a project page
    if (!route.path.startsWith('/projects/') || route.path === '/projects/') {
      return;
    }

    // Ensure we have the latest data
    const currentSlug = slug.value;

    // Add a check to make sure slug is valid
    if (!currentSlug) {
      // Silent fallback
      return;
    }

    // Apply theme if we have the data
    if (doc.value?.meta.colors) {
      // Create a project colors object with all necessary properties
      const projectColors = {
        ...doc.value.meta.colors,
        background: doc.value.meta.colors.background || generateDarkBackground(doc.value.meta.colors.primary)
      };

      // Apply the theme using the theme store
      themeStore.setProjectTheme(projectColors);

      // Update document title again to ensure it's set correctly
      document.title = doc.value.title ? `${doc.value.title} | Fadi Nahhas` : 'Project | Fadi Nahhas';
    } else {
      // Silent fallback
    }
  } catch (error) {
    // Silent fallback
    // Try to reset theme as a fallback
    try {
      themeStore.resetToDefaultTheme();
    } catch (resetError) {
      // Silent fallback
    }
    // Make sure loading state is set to false even on error
    isLoading.value = false;
  }
});

// Watch for changes to the project data with improved error handling
watchEffect(() => {
  // Skip during SSR to prevent hydration mismatches
  if (typeof window === 'undefined') return;

  try {
    if (doc.value) {
      if (doc.value.meta.colors) {
        updateProjectTheme();
      } else {
        // Silent fallback
      }
    }
  } catch (error) {
    // Silent fallback
  }
});

// Function to update the theme with project colors
function updateProjectTheme() {
  // Skip during SSR to prevent hydration mismatches
  if (typeof window === 'undefined') return;

  try {
    // Ensure we have valid data
    if (!doc.value || !doc.value.meta.colors) {
      // Silent fallback
      return;
    }

    // Create a new theme object with the project colors
    // If background is not specified in project colors, use a dark shade of the primary color
    const projectColors = {
      ...doc.value.meta.colors,
      // If background is not specified, generate a dark background based on the primary color
      background: doc.value.meta.colors.background || generateDarkBackground(doc.value.meta.colors.primary)
    };

    // Use the theme store to set the project theme
    themeStore.setProjectTheme(projectColors);
  } catch (error) {
    // Silent fallback
    // Attempt to reset to default theme as a fallback
    try {
      themeStore.resetToDefaultTheme();
    } catch (resetError) {
      // Silent fallback
    }
  }
}

// Function to generate a dark background color based on the primary color
function generateDarkBackground(primaryColor) {
  // If no primary color is provided, return a default dark color
  if (!primaryColor) return '#121212';

  // Convert hex to RGB
  let r = parseInt(primaryColor.slice(1, 3), 16);
  let g = parseInt(primaryColor.slice(3, 5), 16);
  let b = parseInt(primaryColor.slice(5, 7), 16);

  // Darken the color (reduce brightness to 15%)
  r = Math.floor(r * 0.15);
  g = Math.floor(g * 0.15);
  b = Math.floor(b * 0.15);

  // Convert back to hex
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

onMounted(() => {
  // Skip all client-side operations during SSR
  if (typeof window === 'undefined') return;

  try {
    // Animate project title with a fancy entrance
    if (projectTitle.value) {
      gsap.from(projectTitle.value, {
        y: 20,
        opacity: 0,
        duration: 0.8,
        ease: 'power3.out',
        delay: 0.2 // Slight delay for better visual flow
      });

      // Add a subtle glow animation to the title
      gsap.to(projectTitle.value, {
        textShadow: `0 0 10px ${doc.value?.colors?.primary || '#00FF41'}80`,
        duration: 1.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut'
      });
    }
  } catch (error) {
    console.error('Error in project page onMounted:', error);
  }
})
</script>

<style scoped>
/* Project-specific styles */
.back-button {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  border: 1px solid; /* Border will use the color from :style binding */
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Content backdrop for better readability - using glass effect */
.content-backdrop {
  position: relative;
  padding: 1.5rem;
  border-radius: 0.75rem;
  background-color: rgba(0, 0, 0, 0.6); /* Dark tinted glass background */
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  box-shadow:
    0 4px 30px rgba(0, 0, 0, 0.2),
    inset 0 0 15px rgba(0, 0, 0, 0.3); /* Inner shadow for thick glass effect */
  overflow: hidden;
  isolation: isolate; /* Create stacking context */
}

/* Glass edge highlight effect - only on top and left edges */
.content-backdrop::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  border-radius: inherit;
  border-top: 1px solid rgba(255, 255, 255, 0.07); /* Top highlight */
  border-left: 1px solid rgba(255, 255, 255, 0.07); /* Left highlight */
  border-right: 1px solid rgba(0, 0, 0, 0.3); /* Right shadow */
  border-bottom: 1px solid rgba(0, 0, 0, 0.3); /* Bottom shadow */
  pointer-events: none;
}

/* Custom styling for markdown content */
.project-content :deep(.prose) {
  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    color: var(--primary-color) !important;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(var(--primary-rgb), 0.3);
    /* Remove anchor links */
    a {
      color: inherit !important;
      text-decoration: none;
      pointer-events: none;
    }
    a:hover {
      text-decoration: none;
    }
    /* Remove hover effects and # symbols */
    &:hover a::before {
      display: none;
    }
  }

  h1 {
    font-size: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.5rem;
  }

  h2 {
    font-size: 1.75rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  /* Paragraphs */
  p {
    color: white !important;
    line-height: 1.7;
    margin-bottom: 1rem;
  }

  /* Links */
  a {
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  a:hover {
    text-decoration: underline;
    opacity: 0.9;
  }

  /* Lists - these styles are for fallback if custom components don't load */
  ul:not(.themed-list), ol:not(.themed-ordered-list) {
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  li:not(.themed-list-item) {
    margin-bottom: 0.5rem;
    color: white !important;
  }

  /* Ensure custom list components have proper spacing */
  .themed-list, .themed-ordered-list {
    margin: 1.5rem 0;
  }

  /* Media components styling */
  .media-carousel-wrapper, .youtube-embed-wrapper {
    margin: 2rem 0;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    background-color: rgba(0, 0, 0, 0.2);
  }

  /* YouTube iframe styling */
  .youtube-embed-wrapper iframe {
    display: block;
    border-radius: 0.25rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }

  /* Gallery styling */
  .gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }

  .gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: zoom-in;
  }

  .gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  }

  .gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .gallery-item:hover img {
    transform: scale(1.05);
  }

  /* Ensure videos display properly */
  .media-carousel video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Code blocks */
  pre {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
  }

  code {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
  }
}
</style>